<?php $__env->startSection('content'); ?>
<div class="flex h-screen bg-admin-gray">
    <!-- Sidebar -->
    <div id="admin-sidebar" class="admin-sidebar fixed inset-y-0 left-0 z-50 w-64 transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
        <div class="flex items-center justify-between h-16 px-4 bg-admin-sidebar border-b border-gray-700">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-shield-alt text-white text-lg"></i>
                </div>
                <h1 class="text-xl font-bold text-white">Admin Panel</h1>
            </div>
            <!-- Sidebar toggle button - visible on all screen sizes -->
            <button id="sidebar-collapse-btn" onclick="toggleSidebarCollapse()" class="p-2 rounded-md text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200" title="Toggle Sidebar">
                <i class="fas fa-angle-left text-lg transition-transform duration-300"></i>
            </button>
        </div>

        <!-- Navigation -->
        <nav class="mt-5 px-2 custom-scrollbar overflow-y-auto h-full pb-20">
            <div class="space-y-1">
                <!-- Dashboard -->
                <a href="<?php echo e(route('admin.dashboard')); ?>"
                   class="admin-nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-white <?php echo e(request()->routeIs('admin.dashboard') ? 'active text-blue-400' : ''); ?>">
                    <i class="fas fa-tachometer-alt mr-3 text-lg"></i>
                    Dashboard
                </a>

                <!-- Applications -->
                <a href="<?php echo e(route('admin.applications')); ?>"
                   class="admin-nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-white <?php echo e(request()->routeIs('admin.applications*') ? 'active text-blue-400' : ''); ?>">
                    <i class="fas fa-file-alt mr-3 text-lg"></i>
                    Applications
                    <?php if(isset($stats['pending_applications']) && $stats['pending_applications'] > 0): ?>
                        <span class="ml-auto bg-red-600 text-white text-xs rounded-full px-2 py-1"><?php echo e($stats['pending_applications']); ?></span>
                    <?php endif; ?>
                </a>

                <!-- Countries -->
                <a href="<?php echo e(route('admin.countries')); ?>"
                   class="admin-nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-white <?php echo e(request()->routeIs('admin.countries*') ? 'active text-blue-400' : ''); ?>">
                    <i class="fas fa-globe mr-3 text-lg"></i>
                    Countries
                </a>

                <!-- Users -->
                <a href="<?php echo e(route('admin.users')); ?>"
                   class="admin-nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-white <?php echo e(request()->routeIs('admin.users*') ? 'active text-blue-400' : ''); ?>">
                    <i class="fas fa-users mr-3 text-lg"></i>
                    Users
                </a>

                <!-- Reports -->
                <a href="<?php echo e(route('admin.reports')); ?>"
                   class="admin-nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-white <?php echo e(request()->routeIs('admin.reports*') ? 'active text-blue-400' : ''); ?>">
                    <i class="fas fa-chart-bar mr-3 text-lg"></i>
                    Reports
                </a>

                <!-- Settings -->
                <a href="<?php echo e(route('admin.settings')); ?>"
                   class="admin-nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-white <?php echo e(request()->routeIs('admin.settings*') ? 'active text-blue-400' : ''); ?>">
                    <i class="fas fa-cog mr-3 text-lg"></i>
                    Settings
                </a>

                <!-- Divider -->
                <div class="border-t border-gray-700 my-4"></div>

                <!-- Quick Stats -->
                <div class="px-2 py-4">
                    <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">Quick Stats</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Total Apps</span>
                            <span class="text-white font-medium"><?php echo e($stats['total_applications'] ?? 0); ?></span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Pending</span>
                            <span class="text-yellow-400 font-medium"><?php echo e($stats['pending_applications'] ?? 0); ?></span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Revenue</span>
                            <span class="text-green-400 font-medium">€<?php echo e(number_format($stats['total_revenue'] ?? 0, 0)); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-4 py-4">
                <div class="flex items-center">
                    <!-- Mobile menu button (only for mobile overlay) -->
                    <button id="sidebar-toggle" onclick="toggleSidebar()" class="lg:hidden p-3 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-all duration-200">
                        <span class="icon">
                            <i class="fas fa-bars text-xl"></i>
                        </span>
                    </button>

                    <!-- Desktop sidebar toggle button -->
                    <button id="desktop-sidebar-toggle" onclick="toggleSidebarCollapse()" class="hidden lg:block p-3 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-all duration-200" title="Toggle Sidebar">
                        <span class="icon">
                            <i class="fas fa-angle-left text-xl transition-transform duration-300"></i>
                        </span>
                    </button>

                    <div class="ml-4 lg:ml-0">
                        <h1 class="text-2xl font-bold text-gray-900"><?php echo $__env->yieldContent('page-title', 'Dashboard'); ?></h1>
                        <p class="text-sm text-gray-500"><?php echo $__env->yieldContent('page-description', 'Welcome to the admin dashboard'); ?></p>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full">
                            <i class="fas fa-bell text-lg"></i>
                            <?php if(isset($stats['pending_applications']) && $stats['pending_applications'] > 0): ?>
                                <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"><?php echo e(min($stats['pending_applications'], 9)); ?></span>
                            <?php endif; ?>
                        </button>
                    </div>

                    <!-- User Menu -->
                    <div class="relative">
                        <div class="flex items-center space-x-3">
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900"><?php echo e(auth()->user()->name); ?></p>
                                <p class="text-xs text-gray-500">Administrator</p>
                            </div>
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Logout -->
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="p-2 text-gray-400 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-full" title="Logout">
                            <i class="fas fa-sign-out-alt text-lg"></i>
                        </button>
                    </form>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-admin-gray">
            <div class="container mx-auto px-6 py-8">
                <?php echo $__env->yieldContent('admin-content'); ?>
            </div>
        </main>
    </div>
</div>

<!-- Mobile Sidebar Overlay -->
<div class="admin-sidebar-overlay lg:hidden" onclick="closeSidebar()"></div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\eu-drivinglicence\resources\views/layouts/admin.blade.php ENDPATH**/ ?>