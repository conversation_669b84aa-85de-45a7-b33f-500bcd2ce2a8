<?php $__env->startSection('head'); ?>
<title>Settings - Admin Dashboard</title>
<meta name="description" content="Admin settings dashboard">
<meta name="robots" content="noindex, nofollow">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">Settings</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-700"><?php echo e(auth()->user()->name); ?></span>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt mr-1"></i> Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <?php if(session('success')): ?>
            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <i class="fas fa-check-circle mr-2"></i><?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <i class="fas fa-exclamation-circle mr-2"></i><?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>

        <!-- Settings Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- System Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>System Information
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Laravel Version</dt>
                            <dd class="text-sm text-gray-900"><?php echo e(app()->version()); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">PHP Version</dt>
                            <dd class="text-sm text-gray-900"><?php echo e(PHP_VERSION); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Database</dt>
                            <dd class="text-sm text-gray-900"><?php echo e(config('database.default')); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Environment</dt>
                            <dd class="text-sm text-gray-900"><?php echo e(app()->environment()); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Debug Mode</dt>
                            <dd class="text-sm text-gray-900"><?php echo e(config('app.debug') ? 'Enabled' : 'Disabled'); ?></dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Application Settings -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        <i class="fas fa-cogs text-green-500 mr-2"></i>Application Settings
                    </h3>

                    <form method="POST" action="<?php echo e(route('admin.settings.update')); ?>" class="space-y-4">
                        <?php echo csrf_field(); ?>

                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                <div>
                                    <h4 class="text-sm font-medium text-blue-800">Settings Panel</h4>
                                    <p class="text-sm text-blue-600">Application settings will be available here in future updates.</p>
                                </div>
                            </div>
                        </div>

                        <div class="pt-4">
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm transition-colors">
                                <i class="fas fa-save mr-2"></i>Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        <i class="fas fa-bolt text-yellow-500 mr-2"></i>Quick Actions
                    </h3>
                    <div class="space-y-3">
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="w-full text-left px-4 py-3 text-sm bg-blue-50 text-blue-700 hover:bg-blue-100 rounded-md border border-blue-200 transition-colors block">
                            <i class="fas fa-tachometer-alt mr-2"></i>Go to Dashboard
                        </a>
                        <a href="<?php echo e(route('admin.applications')); ?>" class="w-full text-left px-4 py-3 text-sm bg-green-50 text-green-700 hover:bg-green-100 rounded-md border border-green-200 transition-colors block">
                            <i class="fas fa-file-alt mr-2"></i>Manage Applications
                        </a>
                        <a href="<?php echo e(route('admin.countries')); ?>" class="w-full text-left px-4 py-3 text-sm bg-purple-50 text-purple-700 hover:bg-purple-100 rounded-md border border-purple-200 transition-colors block">
                            <i class="fas fa-globe mr-2"></i>Manage Countries
                        </a>
                    </div>
                </div>
            </div>

            <!-- Application Statistics -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        <i class="fas fa-chart-bar text-indigo-500 mr-2"></i>Application Statistics
                    </h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-3 rounded-lg text-center">
                            <div class="text-2xl font-bold text-blue-600"><?php echo e(\App\Models\Application::count()); ?></div>
                            <div class="text-xs text-gray-500">Total Applications</div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-lg text-center">
                            <div class="text-2xl font-bold text-green-600"><?php echo e(\App\Models\Application::where('status', 'approved')->count()); ?></div>
                            <div class="text-xs text-gray-500">Approved</div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-lg text-center">
                            <div class="text-2xl font-bold text-yellow-600"><?php echo e(\App\Models\Application::where('status', 'pending')->count()); ?></div>
                            <div class="text-xs text-gray-500">Pending</div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-lg text-center">
                            <div class="text-2xl font-bold text-red-600"><?php echo e(\App\Models\Application::where('status', 'rejected')->count()); ?></div>
                            <div class="text-xs text-gray-500">Rejected</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\eu-drivinglicence\resources\views/admin/settings.blade.php ENDPATH**/ ?>